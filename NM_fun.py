import numpy as np
import networkx as nx
import random
from collections import Counter
from typing import List, Set, Tuple, Dict, Optional
import matplotlib.pyplot as plt

from base_fun import IC, PRE, gen_graph


# ---------------------------
# 离散 NM（DM）各步骤函数
# ---------------------------
#节点的度作为节点评分
def node_score(G: nx.Graph, v: int) -> float:
    return float(G.degree(v))

#调用PRE函数计算目标值
def objective(G: nx.Graph, seed_set: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int) -> float:
    # 适应度：直接使用 PRE（值越大越好）
    return PRE(G, seed_set, p, neighbors, max_hop=max_hop)


# 计算单纯形内各顶点（种子集合）的适应度（越大越好）
def evaluate_simplex(G: nx.Graph, simplex: List[Set[int]], p: float, neighbors: Dict[int, List[int]], max_hop: int) -> List[Tuple[float, Set[int]]]:
    scored = [(objective(G, s, p, neighbors, max_hop), s) for s in simplex]
    scored.sort(key=lambda t: t[0], reverse=True)
    return scored

#随机初始化单纯复形
# def initialize_simplex(G: nx.Graph, n: int, k: int, random_seed: Optional[int] = None) -> List[Set[int]]:
#     if random_seed is not None:
#         random.seed(random_seed)
#         np.random.seed(random_seed)
#     all_nodes: List[int] = list(G.nodes())
#     if len(all_nodes) < k:
#         raise ValueError("图的节点数量小于所需的种子集合大小 k")
#     simplex: List[Set[int]] = []
#     for _ in range(n + 1):
#         cand = set(random.sample(all_nodes, k))
#         simplex.append(cand)
#     return simplex


#根据度中心性初始化种群,单纯复形
def degree_initialization(G: nx.Graph, n: int, k: int, random_seed: Optional[int] = None) -> List[Set[int]]:

    nodes: List[int] = list(G.nodes())
    if len(nodes) < k:
        raise ValueError("图的节点数量小于所需的种子集合大小 k")

    degree_sorted_nodes: List[int] = sorted(nodes, key=lambda node: G.degree(node), reverse=True)

    solutions: List[Set[int]] = []
    for _ in range(n + 1):
        position: List[int] = degree_sorted_nodes[:k]
        for i in range(k):
            if random.random() > 0.5:
                available_nodes = [node for node in nodes if node not in position]
                if available_nodes:
                    position[i] = random.choice(available_nodes)
        solutions.append(set(position))

    return solutions


# 计算除了最差解以外的“离散质心”（频率最高/度更高的节点优先）
def centroid_discrete(G: nx.Graph, simplex_sets: List[Set[int]], exclude_set: Set[int], k: int) -> Set[int]:
    # 统计除最差解外所有解中节点的出现频率
    node_frequency = Counter()
    for solution_set in simplex_sets:
        if solution_set is exclude_set:
            continue
        node_frequency.update(solution_set)

    # 按频率和节点度排序选择候选节点
    candidates = sorted(node_frequency.items(), key=lambda t: (t[1], node_score(G, t[0])), reverse=True)
    chosen_nodes = [node for node, _ in candidates[:k]]

    # 如果候选节点不足k个，补充高度节点
    if len(chosen_nodes) < k:
        all_nodes: List[int] = list(G.nodes())
        remaining_nodes = [node for node in all_nodes if node not in chosen_nodes]
        remaining_nodes.sort(key=lambda node: node_score(G, node), reverse=True)
        chosen_nodes += remaining_nodes[: (k - len(chosen_nodes))]

    # 对最终选出的 k 个节点进行随机替换：对每个节点以 0.5 概率，用网络中不在当前集合的随机节点替换
    # 确保替换后的集合元素唯一且总数保持为 k
    try:
        import random as _rnd
        chosen_set: Set[int] = set(chosen_nodes[:k])
        all_nodes_set: Set[int] = set(G.nodes())
        available_nodes: Set[int] = all_nodes_set - chosen_set
        if available_nodes:
            chosen_list = list(chosen_set)
            for node in chosen_list:
                if _rnd.random() < 0.5 and available_nodes:
                    # 从未被选中的节点中随机挑一个替换当前节点
                    new_node = _rnd.choice(tuple(available_nodes))
                    chosen_set.remove(node)
                    chosen_set.add(new_node)
                    # 更新可用池，避免重复选入
                    available_nodes.discard(new_node)
                    available_nodes.add(node)
        chosen_nodes = list(chosen_set)
    except Exception:
        # 若出现异常（极少数边界情况），退回到原始选择
        pass

    return set(chosen_nodes[:k])


def worst_nodes(G: nx.Graph, node_set: Set[int], num_nodes: int) -> List[int]:
    # 在节点集合中选出度最小的 num_nodes 个节点
    num_nodes = max(0, min(num_nodes, len(node_set)))
    return [node for node, _ in sorted(((node, node_score(G, node)) for node in node_set), key=lambda t: t[1])[:num_nodes]]


def top_nodes(G: nx.Graph, candidate_pool: Set[int], num_nodes: int, base_set: Set[int] | None = None) -> List[int]:
    # 在候选池中选出度最大的 num_nodes 个节点（避免与基础集合重复）
    base_set = base_set or set()
    available_nodes = [node for node in candidate_pool if node not in base_set]
    available_nodes.sort(key=lambda node: node_score(G, node), reverse=True)
    return available_nodes[:max(0, num_nodes)]


# 调整集合大小到 k（若超出则丢弃度小节点，不足则补充度大节点）
def repair_size(G: nx.Graph, node_set: Set[int], target_size: int) -> Set[int]:
    if len(node_set) > target_size:
        nodes_to_remove = len(node_set) - target_size
        nodes_to_drop = worst_nodes(G, node_set, nodes_to_remove)
        node_set = set(node for node in node_set if node not in nodes_to_drop)
    elif len(node_set) < target_size:
        nodes_to_add = target_size - len(node_set)
        all_nodes: Set[int] = set(G.nodes())
        additional_nodes = top_nodes(G, all_nodes - node_set, nodes_to_add)
        node_set = set(node_set) | set(additional_nodes)
    return node_set


# 反射：将“质心”相对最差解进行反射，尝试朝好的方向探索
def reflect_step(G: nx.Graph, x_centroid: Set[int], x_worst: Set[int], alpha: float, k: int, verbose: bool = False) -> Set[int]:
    # 反射方向向量：从最差解指向质心的方向，表示优化的方向
    direction_vector = x_centroid - x_worst  # D: 反射基向量，包含质心中存在但最差解中不存在的节点

    # 将alpha转换为整数：如果alpha < 1，则按比例计算；否则直接取整
    alpha_int = max(1, int(alpha * k)) if alpha < 1 else int(alpha)
    worst_nodes_to_replace = set(worst_nodes(G, x_centroid, alpha_int))  # W: 质心中需要被替换的最差节点
    base_nodes = x_centroid - worst_nodes_to_replace  # 保留的基础节点集合
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))  # 从方向向量中选择的优质节点
    x_reflected = base_nodes | chosen_nodes  # 反射后的解
    x_reflected = repair_size(G, x_reflected, k)
    if verbose:
        print(f"【反射】方向向量大小={len(direction_vector)} 被替换数={len(worst_nodes_to_replace)} -> 反射解大小={len(x_reflected)}")
    return x_reflected


# 扩展：在反射成功的基础上进一步加大步长探索
def expand_step(G: nx.Graph, x_reflected: Set[int], direction_vector: Set[int], gamma: float, k: int, verbose: bool = False) -> Set[int]:
    # 将gamma转换为整数：如果gamma < 1，则按比例计算；否则直接取整
    gamma_int = max(1, int(gamma * k)) if gamma < 1 else int(gamma)
    worst_nodes_to_replace = set(worst_nodes(G, x_reflected, gamma_int))  # Wp: 反射解中需要被替换的最差节点

    base_nodes = x_reflected - worst_nodes_to_replace  # 保留的基础节点集合
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))  # 从扩展方向向量中选择的优质节点
    x_expanded = base_nodes | chosen_nodes  # 扩展后的解
    x_expanded = repair_size(G, x_expanded, k)
    if verbose:
        print(f"【扩展】被替换数={len(worst_nodes_to_replace)} -> 扩展解大小={len(x_expanded)}")
    return x_expanded


# 外部收缩：当反射优于最差解但不如次差解时，向质心方向小幅移动
def contract_outside_step(G: nx.Graph, x_centroid: Set[int], x_worst: Set[int], rho: float, k: int, verbose: bool = False) -> Set[int]:
    # 外部收缩方向向量：从最差解指向质心的方向，用于小幅度收缩
    direction_vector = x_centroid - x_worst  # D: 外部收缩基向量，包含质心中存在但最差解中不存在的节点

    # 将rho转换为整数：如果rho < 1，则按比例计算；否则直接取整
    rho_int = max(1, int(rho * k)) if rho < 1 else int(rho)
    worst_nodes_to_replace = set(worst_nodes(G, x_centroid, rho_int))  # Wr: 质心中需要被替换的最差节点
    base_nodes = x_centroid - worst_nodes_to_replace  # 保留的基础节点集合
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))  # 从方向向量中选择的优质节点
    x_contracted_outside = base_nodes | chosen_nodes  # 外部收缩后的解
    x_contracted_outside = repair_size(G, x_contracted_outside, k)
    if verbose:
        print(f"【外部收缩】被替换数={len(worst_nodes_to_replace)} -> 外收缩解大小={len(x_contracted_outside)}")
    return x_contracted_outside


# 内部收缩：当反射不优于最差解时，从最差解向质心方向靠拢
def contract_inside_step(G: nx.Graph, x_worst: Set[int], x_centroid: Set[int], rho: float, k: int, verbose: bool = False) -> Set[int]:
    # 内部收缩方向向量：从最差解指向质心的方向，用于从最差解向质心靠拢
    direction_vector = x_centroid - x_worst  # D: 内部收缩基向量，包含质心中存在但最差解中不存在的节点

    # 将rho转换为整数：如果rho < 1，则按比例计算；否则直接取整
    rho_int = max(1, int(rho * k)) if rho < 1 else int(rho)
    worst_nodes_to_replace = set(worst_nodes(G, x_worst, rho_int))  # Wr: 最差解中需要被替换的最差节点
    base_nodes = x_worst - worst_nodes_to_replace  # 保留的基础节点集合
    chosen_nodes = set(top_nodes(G, direction_vector, len(worst_nodes_to_replace), base_set=base_nodes))  # 从方向向量中选择的优质节点
    x_contracted_inside = base_nodes | chosen_nodes  # 内部收缩后的解
    x_contracted_inside = repair_size(G, x_contracted_inside, k)
    if verbose:
        print(f"【内部收缩】被替换数={len(worst_nodes_to_replace)} -> 内收缩解大小={len(x_contracted_inside)}")
    return x_contracted_inside


# 回退（收缩）：以最优解为核心，压缩其他解以重新探索
def shrink_step(G: nx.Graph, scored_simplex: List[Tuple[float, Set[int]]], k: int, sigma: float, verbose: bool = False) -> List[Set[int]]:
    _, x_best = scored_simplex[0]  # 获取最优解作为收缩的中心点
    new_simplex = [set(x_best)]  # 新单纯形以最优解开始

    # 收缩比例：确定从每个解中保留多少节点向最优解靠拢
    nodes_to_keep = max(1, int(round(sigma * k)))  # 每个解向最优解收缩时保留的节点数

    for _, x_current in scored_simplex[1:]:
        # 收缩方向：从当前解向最优解收缩，保留当前解中的优质节点
        top_nodes_from_current = top_nodes(G, x_current, nodes_to_keep)  # 从当前解中选择度最高的节点
        x_shrunk = set(x_best) | set(top_nodes_from_current)  # 收缩后的解：最优解 + 当前解的优质节点
        x_shrunk = repair_size(G, x_shrunk, k)  # 调整到指定大小
        new_simplex.append(x_shrunk)

    if verbose:
        print(f"【回退-收缩】保留比例≈{sigma} 实际保留节点数={nodes_to_keep} 新单纯形大小={len(new_simplex)}")
    return new_simplex

