#!/usr/bin/env python3
"""
测试算子统计功能的简单脚本
"""

import networkx as nx
from NM_fun import (
    get_operator_statistics, reset_operator_statistics,
    reflect_step, expand_step, contract_outside_step, contract_inside_step, shrink_step,
    degree_initialization, evaluate_simplex, objective
)
from base_fun import gen_graph

def test_operator_statistics():
    """测试算子统计功能"""
    print("=== 测试算子统计功能 ===")
    
    # 重置统计
    reset_operator_statistics()
    stats = get_operator_statistics()
    
    # 创建一个简单的测试图
    G = nx.erdos_renyi_graph(20, 0.3, seed=42)
    k = 5
    
    # 初始化一些解
    simplex = degree_initialization(G, 3, k)
    
    # 测试反射操作
    x_centroid = simplex[0]
    x_worst = simplex[1]
    
    print("测试反射操作...")
    x_reflected = reflect_step(G, x_centroid, x_worst, alpha=1.0, k=k, stats=stats)
    
    # 手动记录一些统计
    stats.record_reflect(True)  # 模拟成功
    stats.record_reflect(False)  # 模拟失败
    stats.record_expand(True)
    stats.record_contract_outside(False)
    stats.record_contract_inside(True)
    stats.record_shrink(True)
    
    print("统计结果:")
    stats.print_statistics()
    
    print("\n成功率:")
    rates = stats.get_success_rates()
    for op, rate in rates.items():
        print(f"{op}: {rate:.2%}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_operator_statistics()
